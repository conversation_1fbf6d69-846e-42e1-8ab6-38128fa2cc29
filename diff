diff --git a/app/code/Comave/Catalog/Controller/Adminhtml/Category/Image/Upload.php b/app/code/Comave/Catalog/Controller/Adminhtml/Category/Image/Upload.php
index aae562433..68f58fc13 100644
--- a/app/code/Comave/Catalog/Controller/Adminhtml/Category/Image/Upload.php
+++ b/app/code/Comave/Catalog/Controller/Adminhtml/Category/Image/Upload.php
@@ -146,7 +146,16 @@ class Upload extends Action implements HttpPostActionInterface
         return $this->imageUploaderFactory->create([
             'baseTmpPath' => $baseTmpPath,
             'basePath' => $basePath,
-            'allowedExtensions' => self::ALLOWED_EXTENSIONS
+            'allowedExtensions' => self::ALLOWED_EXTENSIONS,
+            'allowedMimeTypes' => [
+                'image/jpg',
+                'image/jpeg',
+                'image/gif',
+                'image/png',
+                'image/webp',
+                'image/avif',
+                'image/svg+xml'
+            ]
         ]);
     }
 }
diff --git a/app/code/Comave/Catalog/Model/Category/Attribute/Backend/OrientationImage.php b/app/code/Comave/Catalog/Model/Category/Attribute/Backend/OrientationImage.php
index a1202d147..3fe86a058 100644
--- a/app/code/Comave/Catalog/Model/Category/Attribute/Backend/OrientationImage.php
+++ b/app/code/Comave/Catalog/Model/Category/Attribute/Backend/OrientationImage.php
@@ -279,7 +279,7 @@ class OrientationImage extends \Magento\Eav\Model\Entity\Attribute\Backend\Abstr
         $uploader = $this->fileUploaderFactory->create(['fileId' => $attributeName]);
         $uploader->setAllowedExtensions(self::ALLOWED_EXTENSIONS);
         $uploader->setAllowRenameFiles(true);
-        $uploader->setFilesDispersion(true);
+        $uploader->setFilesDispersion(false);
 
         $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA);
         $result = $uploader->save($mediaDirectory->getAbsolutePath($uploadDir));
diff --git a/app/code/Comave/Catalog/Plugin/Category/DataProvider.php b/app/code/Comave/Catalog/Plugin/Category/DataProvider.php
index dc7e5f426..09a9f62c9 100644
--- a/app/code/Comave/Catalog/Plugin/Category/DataProvider.php
+++ b/app/code/Comave/Catalog/Plugin/Category/DataProvider.php
@@ -61,7 +61,16 @@ class DataProvider
             }
 
             foreach (self::ORIENTATION_IMAGE_ATTRIBUTES as $attributeCode) {
-                if ($this->hasImageData($categoryData, $attributeCode)) {
+                // First check if we have UI data stored in _additional_data_ attribute
+                $additionalDataKey = '_additional_data_' . $attributeCode;
+
+
+
+                if (isset($categoryData[$additionalDataKey]) && is_array($categoryData[$additionalDataKey])) {
+                    $categoryData[$attributeCode] = $categoryData[$additionalDataKey];
+
+                } elseif ($this->hasImageData($categoryData, $attributeCode)) {
+                    // Fallback: convert string path to UI format
                     $convertedData = $this->convertImageToUiFormat($categoryData[$attributeCode]);
                     if ($convertedData) {
                         $categoryData[$attributeCode] = $convertedData;
diff --git a/app/code/Comave/Catalog/etc/adminhtml/di.xml b/app/code/Comave/Catalog/etc/adminhtml/di.xml
index 426bf4e4d..d0b3ea040 100644
--- a/app/code/Comave/Catalog/etc/adminhtml/di.xml
+++ b/app/code/Comave/Catalog/etc/adminhtml/di.xml
@@ -172,6 +172,7 @@
 
     <type name="Comave\Catalog\Controller\Adminhtml\Category\Image\Upload">
         <arguments>
+            <argument name="imageUploaderFactory" xsi:type="object">Magento\Catalog\Model\ImageUploaderFactory</argument>
             <argument name="logger" xsi:type="object">Psr\Log\LoggerInterface</argument>
         </arguments>
     </type>
diff --git a/app/code/Comave/CatalogGraphQl/Model/Resolver/Category/OrientationImage.php b/app/code/Comave/CatalogGraphQl/Model/Resolver/Category/OrientationImage.php
index d6e41ae54..d7cf0242e 100644
--- a/app/code/Comave/CatalogGraphQl/Model/Resolver/Category/OrientationImage.php
+++ b/app/code/Comave/CatalogGraphQl/Model/Resolver/Category/OrientationImage.php
@@ -2,6 +2,7 @@
 
 namespace Comave\CatalogGraphQl\Model\Resolver\Category;
 
+use Magento\Catalog\Api\CategoryRepositoryInterface;
 use Magento\Catalog\Model\Category;
 use Magento\Framework\Exception\LocalizedException;
 use Magento\Framework\GraphQl\Config\Element\Field;
@@ -22,10 +23,12 @@ class OrientationImage implements ResolverInterface
     public const HTTP_PROTOCOL_PREFIX = 'http';
 
     /**
+     * @param CategoryRepositoryInterface $categoryRepository
      * @param StoreManagerInterface $storeManager
      * @param LoggerInterface $logger
      */
     public function __construct(
+        private readonly CategoryRepositoryInterface $categoryRepository,
         private readonly StoreManagerInterface $storeManager,
         private readonly LoggerInterface $logger
     ) {
@@ -51,7 +54,23 @@ class OrientationImage implements ResolverInterface
         /** @var Category $category */
         $category = $value['model'];
         $attributeName = $field->getName();
-        $imageValue = $category->getData($attributeName);
+
+        // Load category from admin store to match UI behavior
+        $categoryId = $category->getId();
+
+        try {
+            // UI DataProvider uses admin store (store_id: 0), so we should too
+            $adminCategory = $this->categoryRepository->get($categoryId, 0);
+            $imageValue = $adminCategory->getData($attributeName);
+        } catch (\Exception $e) {
+            $this->logger->error('Failed to load category from admin store, using original data', [
+                'category_id' => $categoryId,
+                'error' => $e->getMessage()
+            ]);
+            $imageValue = $category->getData($attributeName);
+        }
+
+
 
         if (!$imageValue) {
             return null;
@@ -62,6 +81,7 @@ class OrientationImage implements ResolverInterface
         if (!$imagePath) {
             return null;
         }
+
         return $this->buildImageUrl($imagePath);
     }
 
diff --git a/app/code/Comave/CatalogGraphQl/etc/di.xml b/app/code/Comave/CatalogGraphQl/etc/di.xml
index 2c7e441b4..7b4aaca55 100644
--- a/app/code/Comave/CatalogGraphQl/etc/di.xml
+++ b/app/code/Comave/CatalogGraphQl/etc/di.xml
@@ -12,6 +12,16 @@
 
     <type name="Comave\CatalogGraphQl\Model\Resolver\Category\OrientationImage">
         <arguments>
+            <argument name="categoryRepository" xsi:type="object">Magento\Catalog\Api\CategoryRepositoryInterface</argument>
+            <argument name="storeManager" xsi:type="object">Magento\Store\Model\StoreManagerInterface</argument>
+            <argument name="logger" xsi:type="object">Psr\Log\LoggerInterface</argument>
+        </arguments>
+    </type>
+
+    <type name="Comave\CatalogGraphQl\Model\Resolver\Category\UploadOrientationImage">
+        <arguments>
+            <argument name="categoryRepository" xsi:type="object">Magento\Catalog\Api\CategoryRepositoryInterface</argument>
+            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem</argument>
             <argument name="storeManager" xsi:type="object">Magento\Store\Model\StoreManagerInterface</argument>
             <argument name="logger" xsi:type="object">Psr\Log\LoggerInterface</argument>
         </arguments>
diff --git a/app/code/Comave/CatalogGraphQl/etc/schema.graphqls b/app/code/Comave/CatalogGraphQl/etc/schema.graphqls
index f079332f0..fc88364cf 100644
--- a/app/code/Comave/CatalogGraphQl/etc/schema.graphqls
+++ b/app/code/Comave/CatalogGraphQl/etc/schema.graphqls
@@ -1,25 +1,53 @@
 type Query {
-    brandsList (
-        pageSize: Int = 20 @doc(description: "The maximum number of results to return at once. The default value is 20."),
-        currentPage: Int = 1 @doc(description: "The page of results to return. The default value is 1."),
+    brandsList(
+        pageSize: Int = 20
+            @doc(
+                description: "The maximum number of results to return at once. The default value is 20."
+            )
+        currentPage: Int = 1
+            @doc(
+                description: "The page of results to return. The default value is 1."
+            )
     ): Brands
-    @resolver(class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Brands") @doc(description: "List of available brands")
-
+        @resolver(class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Brands")
+        @doc(description: "List of available brands")
 }
 
 interface ProductInterface {
-    visible_attributes: [VisibleFrontendAttribute] @doc(description: "Returns all attribute values and labels marked as visible on storefront")
-    @resolver(class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\VisibleAttributes")
-    lix_points: Int! @doc(description: "Returns Lix Points obtained after buying this product")
-    @resolver(class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\LixPointsReward")
-    xp_points: Int! @doc(description: "Returns Comave XP Points obtained after buying this product")
-    @resolver(class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\LixPointsReward")
-    seller: Seller @doc(description: "Returns the seller's details.")
-    @resolver(class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Seller")
-    brand: Brand @doc(description: "Returns Product Brand Information")
-    @resolver(class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\Brand")
-    status: Int! @doc(description: "Returns product status")
-    @resolver(class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\Status")
+    visible_attributes: [VisibleFrontendAttribute]
+        @doc(
+            description: "Returns all attribute values and labels marked as visible on storefront"
+        )
+        @resolver(
+            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\VisibleAttributes"
+        )
+    lix_points: Int!
+        @doc(
+            description: "Returns Lix Points obtained after buying this product"
+        )
+        @resolver(
+            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\LixPointsReward"
+        )
+    xp_points: Int!
+        @doc(
+            description: "Returns Comave XP Points obtained after buying this product"
+        )
+        @resolver(
+            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\LixPointsReward"
+        )
+    seller: Seller
+        @doc(description: "Returns the seller's details.")
+        @resolver(class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Seller")
+    brand: Brand
+        @doc(description: "Returns Product Brand Information")
+        @resolver(
+            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\Brand"
+        )
+    status: Int!
+        @doc(description: "Returns product status")
+        @resolver(
+            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\Status"
+        )
 }
 
 type Brand {
@@ -32,32 +60,45 @@ type Brands @doc(description: "Contains the available brands list.") {
     items: [Brand] @doc(description: "List of available brands.")
     pageSize: Int @doc(description: "The total number of brands per page.")
     currentPage: Int @doc(description: "Current page.")
-    totalPages: Int @doc(description: "Total number of pages for given page size.")
+    totalPages: Int
+        @doc(description: "Total number of pages for given page size.")
     totalCount: Int @doc(description: "The total number of brands in the list.")
-
 }
 
 type ProductImage {
-    role: [String] @doc(description: "Role of the image")
-    @resolver(class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\ProductImageRole")
+    role: [String]
+        @doc(description: "Role of the image")
+        @resolver(
+            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Product\\ProductImageRole"
+        )
 }
 
 type Seller {
     name: String @doc(description: "Seller's full name.")
-    address: String @doc(description: "Seller's default shipping address (Street, County, Country).")
+    address: String
+        @doc(
+            description: "Seller's default shipping address (Street, County, Country)."
+        )
     phone: String @doc(description: "Seller's phone number ('phone_no').")
     email_info: String @doc(description: "Seller's email info ('email_info').")
 }
 
 type VisibleFrontendAttribute {
     label: String! @doc(description: "Attribute label")
-    values: [String!] @doc(description: "Attribute values or attribute option values for selects and multiselects")
+    values: [String!]
+        @doc(
+            description: "Attribute values or attribute option values for selects and multiselects"
+        )
     type: String! @doc(description: "Attribute type")
 }
 
 input ProductAttributeFilterInput {
-    brand: FilterEqualTypeInput @doc(description: "Product Data filter with Brand Value")
-    status: FilterEqualTypeInput @doc(description: "Filter products by status (1 for Enabled, 2 for Disabled).")
+    brand: FilterEqualTypeInput
+        @doc(description: "Product Data filter with Brand Value")
+    status: FilterEqualTypeInput
+        @doc(
+            description: "Filter products by status (1 for Enabled, 2 for Disabled)."
+        )
 }
 
 interface CategoryInterface {
@@ -72,3 +113,34 @@ interface CategoryInterface {
             class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Category\\OrientationImage"
         )
 }
+
+type Mutation {
+    uploadCategoryOrientationImage(
+        input: CategoryOrientationImageUploadInput!
+    ): CategoryOrientationImageUploadOutput
+        @doc(description: "Upload horizontal or vertical image for category")
+        @resolver(
+            class: "\\Comave\\CatalogGraphQl\\Model\\Resolver\\Category\\UploadOrientationImage"
+        )
+}
+
+input CategoryOrientationImageUploadInput {
+    category_id: Int! @doc(description: "Category ID")
+    orientation: CategoryImageOrientation!
+        @doc(description: "Image orientation (horizontal or vertical)")
+    name: String! @doc(description: "File name with extension")
+    base64_encoded_file: String! @doc(description: "Base64 encoded image data")
+    type: String! @doc(description: "Image MIME type")
+}
+
+enum CategoryImageOrientation {
+    HORIZONTAL
+    VERTICAL
+}
+
+type CategoryOrientationImageUploadOutput {
+    success: Boolean! @doc(description: "Upload success status")
+    message: String @doc(description: "Success or error message")
+    image_url: String @doc(description: "Full URL of uploaded image")
+    file_path: String @doc(description: "Relative file path")
+}
