<?php

namespace Comave\Catalog\Model\Category\Attribute\Backend;

use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\Framework\UrlInterface;
use Magento\MediaStorage\Model\File\UploaderFactory;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Unified backend model for category orientation images (horizontal/vertical)
 */
class OrientationImage extends \Magento\Eav\Model\Entity\Attribute\Backend\AbstractBackend
{
    /**
     * Base upload directory for orientation images
     */
    public const BASE_UPLOAD_DIR = 'catalog/category';

    /**
     * Allowed file extensions
     */
    public const ALLOWED_EXTENSIONS = ['gif', 'jpg', 'png', 'jpeg', 'webp', 'avif', 'jfif', 'svg'];

    /**
     * Additional data prefix for UI components
     */
    public const ADDITIONAL_DATA_PREFIX = '_additional_data_';

    /**
     * @param UploaderFactory $fileUploaderFactory
     * @param Filesystem $filesystem
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly UploaderFactory $fileUploaderFactory,
        private readonly Filesystem $filesystem,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Process image upload before saving
     *
     * @param \Magento\Framework\DataObject $object
     * @return $this
     * @throws LocalizedException
     */
    public function beforeSave($object): self
    {
        $attributeName = $this->getAttribute()->getName();
        $value = $object->getData($attributeName);

        $this->logger->info('NOTHING UPLOAD - Backend Model beforeSave', [
            'attribute_name' => $attributeName,
            'value' => $value,
            'value_type' => gettype($value),
            'is_upload_data' => $this->isUploadData($value),
            'is_gallery_selection' => $this->isGallerySelection($value),
            'should_clear' => $this->shouldClearAttribute($value, $object, $attributeName)
        ]);

        if ($this->isUploadData($value)) {
            $this->processImageUpload($object, $attributeName, $value);
        } elseif ($this->isGallerySelection($value)) {
            $this->processGallerySelection($object, $attributeName, $value);
        } elseif ($this->shouldClearAttribute($value, $object, $attributeName)) {
            $object->setData($attributeName, null);
        } elseif (is_string($value) && strpos($value, 'media/') === 0) {
            $cleanPath = substr($value, 6);
            $cleanPath = $this->ensureOrientationPath($cleanPath, $attributeName);
            $object->setData($attributeName, $cleanPath);

        } elseif (is_string($value) && !empty($value) && strpos($value, 'catalog/category/') === 0) {
            $correctedPath = $this->ensureOrientationPath($value, $attributeName);
            if ($correctedPath !== $value) {
                $object->setData($attributeName, $correctedPath);
            }
        } else {
            $originalValue = $object->getOrigData($attributeName);
            if ($originalValue !== null && ($value === null || $value === '')) {
                $object->setData($attributeName, $originalValue);
            }
        }

        return parent::beforeSave($object);
    }

    /**
     * Check if value contains upload data
     *
     * @param mixed $value
     * @return bool
     */
    private function isUploadData($value): bool
    {
        return is_array($value) && isset($value[0]) &&
               (isset($value[0]['tmp_name']) || (isset($value[0]['file']) && isset($value[0]['url'])));
    }

    /**
     * Check if value is gallery selection (existing image)
     *
     * @param mixed $value
     * @return bool
     */
    private function isGallerySelection($value): bool
    {
        return is_array($value) && isset($value[0]) &&
               isset($value[0]['url']) && isset($value[0]['name']) &&
               !isset($value[0]['tmp_name']) &&
               strpos($value[0]['url'], '/tmp/') === false;
    }

    /**
     * Process gallery selection (existing image)
     *
     * @param mixed $object
     * @param string $attributeName
     * @param array $value
     * @return void
     */
    private function processGallerySelection($object, string $attributeName, array $value): void
    {
        $fileData = $value[0];
        $imageUrl = $fileData['url'];

        $mediaUrl = $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
        $relativePath = str_replace($mediaUrl, '', $imageUrl);
        $relativePath = ltrim($relativePath, '/');

        if (strpos($relativePath, 'media/') === 0) {
            $relativePath = substr($relativePath, 6);
        }

        $finalPath = $this->ensureOrientationPath($relativePath, $attributeName);
        $object->setData($attributeName, $finalPath);
    }

    /**
     * Determine if attribute should be cleared
     *
     * @param mixed $value
     * @param \Magento\Framework\DataObject $object
     * @param string $attributeName
     * @return bool
     */
    private function shouldClearAttribute($value, $object, string $attributeName): bool
    {
        if (is_array($value) && isset($value['delete']) && $value['delete']) {
            return true;
        }

        return false;
    }

    /**
     * Process image upload
     *
     * @param \Magento\Framework\DataObject $object
     * @param string $attributeName
     * @param array $value
     * @throws LocalizedException
     */
    private function processImageUpload($object, string $attributeName, array $value): void
    {
        $imageName = $this->getUploadedImageName($value);
        if (!$imageName) {
            return;
        }

        try {
            $fileData = $value[0] ?? $value;
            
            if ($this->isUiComponentUpload($fileData)) {
                $finalPath = $this->processUiComponentUpload($attributeName, $fileData);
            } elseif ($this->isMediaGalleryUpload($fileData)) {
                $finalPath = $this->processMediaGalleryUpload($fileData);
            } else {
                $finalPath = $this->processDirectUpload($attributeName);
            }

            if ($finalPath) {
                $this->setImageData($object, $attributeName, $finalPath, $value);
            }
        } catch (\Exception $e) {
            $this->logger->error('Category orientation image processing failed', [
                'exception' => $e->getMessage(),
                'attribute_name' => $attributeName,
                'orientation' => $this->getOrientation($attributeName),
                'trace' => $e->getTraceAsString()
            ]);

            if ($e->getCode() != \Magento\MediaStorage\Model\File\Uploader::TMP_NAME_EMPTY) {
                throw new LocalizedException(
                    __('Failed to save %1: %2', $this->getOrientationLabel($attributeName), $e->getMessage())
                );
            }
        }
    }

    /**
     * Check if this is a UI component upload (file in tmp directory)
     *
     * @param array $fileData
     * @return bool
     */
    private function isUiComponentUpload(array $fileData): bool
    {
        return isset($fileData['file']) && isset($fileData['url']) && strpos($fileData['url'], '/tmp/') !== false;
    }

    /**
     * Check if this is a media gallery upload (file already in final location)
     *
     * @param array $fileData
     * @return bool
     */
    private function isMediaGalleryUpload(array $fileData): bool
    {
        return isset($fileData['file']) && isset($fileData['url']) && strpos($fileData['url'], '/tmp/') === false;
    }



    /**
     * Process UI component upload (file already in tmp directory)
     *
     * @param string $attributeName
     * @param array $fileData
     * @return string|null
     */
    private function processUiComponentUpload(string $attributeName, array $fileData): ?string
    {
        $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA);
        $orientation = $this->getOrientation($attributeName);
        
        $tmpPath = "tmp/catalog/category/{$orientation}" . $fileData['file'];
        $finalPath = self::BASE_UPLOAD_DIR . "/{$orientation}" . $fileData['file'];
        
        if (!$mediaDirectory->isExist($tmpPath)) {
            throw new LocalizedException(__('Temporary file not found: %1', $tmpPath));
        }

        $mediaDirectory->copyFile($tmpPath, $finalPath);
        $mediaDirectory->delete($tmpPath);
        
        return $finalPath;
    }

    /**
     * Process media gallery upload (file already in final location)
     *
     * @param array $fileData
     * @return string|null
     */
    private function processMediaGalleryUpload(array $fileData): ?string
    {
        $baseUrl = $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
        $relativePath = str_replace($baseUrl, '', $fileData['url']);

        $mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);
        if (!$mediaDirectory->isExist($relativePath)) {
            throw new LocalizedException(__('Media gallery file not found: %1', $relativePath));
        }



        return $relativePath;
    }

    /**
     * Process direct upload
     *
     * @param string $attributeName
     * @return string
     */
    private function processDirectUpload(string $attributeName): string
    {
        $orientation = $this->getOrientation($attributeName);
        $uploadDir = self::BASE_UPLOAD_DIR . "/{$orientation}";

        $uploader = $this->fileUploaderFactory->create(['fileId' => $attributeName]);
        $uploader->setAllowedExtensions(self::ALLOWED_EXTENSIONS);
        $uploader->setAllowRenameFiles(true);
        $uploader->setFilesDispersion(false);

        $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA);
        $result = $uploader->save($mediaDirectory->getAbsolutePath($uploadDir));

        return $uploadDir . '/' . $result['file'];
    }

    /**
     * Set image data on object
     *
     * @param \Magento\Framework\DataObject $object
     * @param string $attributeName
     * @param string $finalPath
     * @param array $value
     */
    private function setImageData($object, string $attributeName, string $finalPath, array $value): void
    {
        $baseUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
        $finalUrl = $baseUrl . $finalPath;
        $value[0]['url'] = $finalUrl;
        $value[0]['name'] = $finalUrl;

        $object->setData(self::ADDITIONAL_DATA_PREFIX . $attributeName, $value);
        $object->setData($attributeName, $finalPath);


    }

    /**
     * Get orientation from attribute name
     *
     * @param string $attributeName
     * @return string
     */
    private function getOrientationFromAttribute(string $attributeName): string
    {
        if (strpos($attributeName, 'horizontal') !== false) {
            return 'horizontal';
        } elseif (strpos($attributeName, 'vertical') !== false) {
            return 'vertical';
        }
        return 'horizontal';
    }

    /**
     * Ensure path includes orientation folder
     *
     * @param string $path
     * @param string $attributeName
     * @return string
     */
    private function ensureOrientationPath(string $path, string $attributeName): string
    {
        $orientation = $this->getOrientationFromAttribute($attributeName);
        $expectedPrefix = "catalog/category/{$orientation}/";

        if (strpos($path, $expectedPrefix) === 0) {
            return $path;
        }

        if (strpos($path, 'catalog/category/') === 0) {
            $filename = basename($path);
            $newPath = $expectedPrefix . $filename;

            $this->ensureFileInCorrectLocation($path, $newPath);
            return $newPath;
        }

        $filename = basename($path);
        return $expectedPrefix . $filename;
    }

    /**
     * Ensure file exists in the correct orientation folder
     *
     * @param string $originalPath
     * @param string $newPath
     * @return void
     */
    private function ensureFileInCorrectLocation(string $originalPath, string $newPath): void
    {
        try {
            $mediaDirectory = $this->filesystem->getDirectoryWrite(\Magento\Framework\App\Filesystem\DirectoryList::MEDIA);
            if ($mediaDirectory->isExist($originalPath) && !$mediaDirectory->isExist($newPath)) {
                $orientationDir = dirname($newPath);
                $mediaDirectory->create($orientationDir);
                $mediaDirectory->copyFile($originalPath, $newPath);
            }
        } catch (\Exception $e) {
            $this->logger->error('Failed to ensure file in correct orientation location', [
                'exception' => $e->getMessage(),
                'original_path' => $originalPath,
                'new_path' => $newPath,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Get uploaded image name from value array
     *
     * @param mixed $value
     * @return string
     */
    private function getUploadedImageName($value): string
    {
        if (!is_array($value)) {
            return '';
        }

        if (isset($value[0]['name'])) {
            return $value[0]['name'];
        }
        
        if (isset($value['name'])) {
            return $value['name'];
        }

        return '';
    }

    /**
     * Get orientation from attribute name
     *
     * @param string $attributeName
     * @return string
     */
    private function getOrientation(string $attributeName): string
    {
        return strpos($attributeName, 'horizontal') !== false ? 'horizontal' : 'vertical';
    }

    /**
     * Get orientation label for error messages
     *
     * @param string $attributeName
     * @return string
     */
    private function getOrientationLabel(string $attributeName): string
    {
        return $this->getOrientation($attributeName) . ' image';
    }
}
