<?php

namespace Comave\CatalogGraphQl\Model\Resolver\Category;

use Magento\Catalog\Api\CategoryRepositoryInterface;

use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * GraphQL resolver for uploading category orientation images via base64
 */
class UploadOrientationImage implements ResolverInterface
{
    /**
     * Base upload directory for orientation images
     */
    public const BASE_UPLOAD_DIR = 'catalog/category';

    /**
     * Allowed file extensions
     */
    public const ALLOWED_EXTENSIONS = ['gif', 'jpg', 'png', 'jpeg', 'webp', 'avif', 'jfif', 'svg'];

    /**
     * Orientation mappings
     */
    public const ORIENTATION_MAPPING = [
        'HORIZONTAL' => 'horizontal',
        'VERTICAL' => 'vertical'
    ];

    /**
     * @param CategoryRepositoryInterface $categoryRepository
     * @param Filesystem $filesystem
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly CategoryRepositoryInterface $categoryRepository,
        private readonly Filesystem $filesystem,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Upload category orientation image via GraphQL
     *
     * @param Field $field
     * @param mixed $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return array
     * @throws GraphQlInputException
     * @throws GraphQlAuthorizationException
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null): array
    {
        // Check if user has admin authorization
        // if (!$context->getUserId() && !$this->isAdminContext($context)) {
        //     throw new GraphQlAuthorizationException(__('The current user is not authorized to upload category images.'));
        // }

        $input = $args['input'] ?? [];
        $this->validateInput($input);

        try {
            $categoryId = (int) $input['category_id'];
            $orientation = strtolower(self::ORIENTATION_MAPPING[$input['orientation']] ?? 'horizontal');
            $attributeName = $orientation . '_image';

            // Load category from admin store (store_id: 0) to match where UI saves/reads
            $category = $this->categoryRepository->get($categoryId, 0);

            $this->logger->info('MISMATCH DEBUG - GraphQL Upload Before Processing', [
                'category_id' => $categoryId,
                'store_id' => 0,
                'attribute_name' => $attributeName,
                'current_value' => $category->getData($attributeName),
                'orientation' => $orientation
            ]);

            // Process base64 image upload
            $uploadResult = $this->processBase64Upload($input, $orientation);

            // Create UI component compatible data structure
            $imageData = [
                [
                    'file' => $uploadResult['file_path'],
                    'url' => $uploadResult['tmp_url'],
                    'name' => $input['name'],
                    'type' => $input['type']
                ]
            ];

            $this->logger->info('MISMATCH DEBUG - GraphQL Upload Setting Data', [
                'upload_result' => $uploadResult,
                'image_data' => $imageData,
                'orientation' => $orientation,
                'attribute_name' => $attributeName
            ]);

            // Set the image data on category (this will trigger the backend model)
            $category->setData($attributeName, $imageData);

            $this->logger->info('MISMATCH DEBUG - GraphQL Upload Before Save', [
                'category_id' => $categoryId,
                'attribute_name' => $attributeName,
                'data_after_set' => $category->getData($attributeName),
                'has_data_changed' => $category->hasDataChanges()
            ]);

            // Save category (this will process the upload through OrientationImage backend model)
            $savedCategory = $this->categoryRepository->save($category);

            $this->logger->info('MISMATCH DEBUG - GraphQL Upload After Save', [
                'category_id' => $categoryId,
                'attribute_name' => $attributeName,
                'saved_value' => $savedCategory->getData($attributeName),
                'reloaded_value' => $this->categoryRepository->get($categoryId, 0)->getData($attributeName)
            ]);

            // Get the final image URL and path after processing
            $categoryImageData = $category->getData($attributeName);
            $finalImageUrl = $this->buildFinalImageUrl($categoryImageData);
            $finalFilePath = $this->extractFilePath($categoryImageData);

            return [
                'success' => true,
                'message' => 'Image uploaded successfully',
                'image_url' => $finalImageUrl,
                'file_path' => $finalFilePath
            ];

        } catch (\Exception $e) {
            $this->logger->error('Category orientation image GraphQL upload failed', [
                'exception' => $e->getMessage(),
                'input' => $input,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'image_url' => null,
                'file_path' => null
            ];
        }
    }

    /**
     * Validate input parameters
     *
     * @param array $input
     * @throws GraphQlInputException
     */
    private function validateInput(array $input): void
    {
        $requiredFields = ['category_id', 'orientation', 'name', 'base64_encoded_file', 'type'];
        
        foreach ($requiredFields as $field) {
            if (empty($input[$field])) {
                throw new GraphQlInputException(__('Field "%1" is required.', $field));
            }
        }

        if (!isset(self::ORIENTATION_MAPPING[$input['orientation']])) {
            throw new GraphQlInputException(__('Invalid orientation. Must be HORIZONTAL or VERTICAL.'));
        }

        // Validate file extension
        $extension = strtolower(pathinfo($input['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, self::ALLOWED_EXTENSIONS, true)) {
            throw new GraphQlInputException(__('Invalid file extension. Allowed: %1', implode(', ', self::ALLOWED_EXTENSIONS)));
        }
    }

    /**
     * Process base64 upload to temporary directory
     *
     * @param array $input
     * @param string $orientation
     * @return array
     * @throws LocalizedException
     */
    private function processBase64Upload(array $input, string $orientation): array
    {
        try {
            // Decode base64 data
            $imageData = base64_decode($input['base64_encoded_file']);
            if ($imageData === false) {
                throw new LocalizedException(__('Invalid base64 image data'));
            }

            // Create file directly in tmp directory
            $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA);
            $tmpPath = "tmp/catalog/category/{$orientation}";

            // Ensure directory exists
            if (!$mediaDirectory->isExist($tmpPath)) {
                $mediaDirectory->create($tmpPath);
            }

            // Generate unique filename
            $fileName = uniqid() . '_' . $input['name'];
            $filePath = $tmpPath . '/' . $fileName;

            // Write file
            $mediaDirectory->writeFile($filePath, $imageData);

            // Build URL
            $mediaUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
            $fileUrl = $mediaUrl . $filePath;

            $this->logger->info('GraphQL Upload Success', [
                'file_path' => $filePath,
                'file_url' => $fileUrl,
                'orientation' => $orientation,
                'original_name' => $input['name']
            ]);

            return [
                'file_path' => '/' . $fileName,
                'tmp_url' => $fileUrl,
                'name' => $input['name']
            ];
        } catch (\Exception $e) {
            $this->logger->error('GraphQL Upload Failed', [
                'exception' => $e->getMessage(),
                'input' => array_merge($input, ['base64_encoded_file' => 'TRUNCATED']),
                'orientation' => $orientation,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Build final image URL from processed category data
     *
     * @param mixed $imageData
     * @return string|null
     */
    private function buildFinalImageUrl($imageData): ?string
    {
        if (is_string($imageData) && !empty($imageData)) {
            $mediaUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
            return $mediaUrl . ltrim($imageData, '/');
        }

        if (is_array($imageData) && isset($imageData[0]['url'])) {
            return $imageData[0]['url'];
        }

        return null;
    }

    /**
     * Extract file path from processed category data
     *
     * @param mixed $imageData
     * @return string|null
     */
    private function extractFilePath($imageData): ?string
    {
        if (is_string($imageData) && !empty($imageData)) {
            return $imageData;
        }

        if (is_array($imageData) && isset($imageData[0]['file'])) {
            return $imageData[0]['file'];
        }

        return null;
    }



    /**
     * Check if the context has admin authorization
     *
     * @param mixed $context
     * @return bool
     */
    private function isAdminContext($context): bool
    {
        // Check if this is an admin context (has admin user type or admin token)
        return $context->getUserType() === \Magento\Authorization\Model\UserContextInterface::USER_TYPE_ADMIN ||
               $context->getUserType() === \Magento\Authorization\Model\UserContextInterface::USER_TYPE_INTEGRATION;
    }
}
